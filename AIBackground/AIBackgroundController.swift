//
//  AIBackgroundController.swift
//  LVEditor
//
//  Created by ByteDance on 5/21/24.
//

import LVUIKit
import LMLogger
import BeeNotify
import PromiseKit
import Foundation
import LVVideoEditor
import LVEditorCommon
import LVEditorBase
import LVNextEditorCore
import LVCanvasRatioAPI

protocol AIBackgroundService: AnyObject {
    func tapOnCanvas()
    func isPanelShow() -> Bool
    func getView() -> MenuPrototype?
    func toggleAIBackgroundMenu(segment: LVMediaSegment)
    func showCancelExitAlertIfNeeded(confirmAction: @escaping () -> Void)
    func showCancelExportAlertIfNeeded(confirmAction: @escaping () -> Void, cancelAction: (() -> Void)?)
    func showCleanAIBackgroundAlertIfNeed(segment: SegmentVideo, type: MattingMenuType)
    func directCancelAIBackgroundIfNeed(confirmAction: @escaping ()->Void)
}

protocol AIBackgroundControllerIsolation: AnyObject {
    func showBindingPhoneNumberAlertIfNeed(completion: @escaping () -> Void)
    var agreementLinks: [String] { get }
}

private let Tag = "AIBackgroundController"

class AIBackgroundController: AIBackgroundService, AIBackgroundControllerIsolation {
    
    var canvasRatioAbility: CanvasRatioAbilityInterface? {
        return context.editorSession?.abilityContainer?.resolve(CanvasRatioAbilityInterface.self)
    }
    
    private(set) var view: AIBackgroundPanelView?
    
    let bag = DisposeBag()
    
    let context: BusinessContext
    
    let viewModel: AIBackgroundViewModel
    
    let undoRedoHandler: UndoRedoHandlerImpl
    
    var undoRedoHandlerID: UndoRedoManager.ChainNodeIdentify?
 
    init(context: BusinessContext) {
        self.context = context
        self.viewModel = AIBackgroundViewModel(draftID: context.draft.draftID)
        self.undoRedoHandler = UndoRedoHandlerImpl(context: context)
        addObserver()
        cleanAllUnlessConfigs()
    }
}

// MARK: - Observer
extension AIBackgroundController {
    private func addObserver() {
        viewModel.$panelStatus.bind(self) { target, change in
            guard change.new != change.old else { return }
            target.view?.updatePanel(with: change.new)
            target.switchGestureMode()
        }.disposed(by: bag)
        
        viewModel.$needResetPreviewResult.bind(self) { target, change in
            guard change.new != change.old else { return }
            target.switchGestureMode()
        }.disposed(by: bag)
        
        context.store.notify.listen { [weak self] _, new in
            guard self?.viewModel.isEditMode == true else { return }
            self?.updateSelectStatus()
            self?.setMosaicBackgroundIfNeed(with: new)
            self?.trackResize(new)
        }.disposed(by: bag)
        
        context.willQuitEditNotify.bind(self) {[weak self] (target, value) in
            guard let self = self else { return }
            // 退出草稿时，清除任务
            AsyncTaskManager.shared.cleanUselessTasks(draftID: self.context.draft.draftID, type: .aiBackgroundTask)
        }.disposed(by: bag)
    }
    
    private func setupDelegate() {
        guard let aiBackgroundPanelView = view else { return }
        setupPanelActionDelegate(with: aiBackgroundPanelView)
        setupDraftActionDelegate(with: aiBackgroundPanelView)
        setupAlertActionDelegate(with: aiBackgroundPanelView)
        setupEnableUndoActionDelegate(with: aiBackgroundPanelView)
    }
    
    private func setupPanelActionDelegate(with panel: AIBackgroundPanelView) {
        panel.actionDelegates.panelAction.delegate(on: self) { target, action in
            switch action {
            case .show:
                target.enterEditMode()
                target.showToastInNeed()
                target.viewModel.needRecoverMatting = true
            case .dismiss:
                target.exitEditMode()
            }
        }
    }
    
    private func setupDraftActionDelegate(with panel: AIBackgroundPanelView) {
        panel.actionDelegates.draftAction.delegate(on: self) { target, action in
            switch action {
            case .genBackground:
                target.exportCurrentMattingFrame() {
                    // UI 在主线程调度
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }
                        self.view?.tryGenerateAIBackground(with: self.viewModel.prompt)
                    }
                }
            case .resetBackground:
                // 通过清除按钮点击的，original 的尺寸可能是不同的
                let isOriginal = target.ratio == .original
                target.resetAIBackground(setUndoRedo: !isOriginal, needResetOriginCanvas: isOriginal)
            case .previewBackground:
                target.previewAIBackground()
            case .setRatio:
                if target.viewModel.needResetPreviewResult {
                    target.resetAIBackground(setUndoRedo: false)
                    target.view?.resetPreviewView()
                }
                target.setCanvas(target.viewModel.ratio)
            }
        }
    }
    
    private func setupAlertActionDelegate(with panel: AIBackgroundPanelView) {
        panel.actionDelegates.alertAction.delegate(on: self) { target, action in
            switch action {
            case .back:
                target.showExitBackgroundAlert()
            case .reGenerate:
                target.showRegenerateAlert()
            case .bindingPhone:
                target.showBindingPhoneNumberAlertIfNeed() { [weak self] in
                    self?.view?.generateAIBackground()
                }
            }
        }
    }
    
    private func setupEnableUndoActionDelegate(with panel: AIBackgroundPanelView) {
        panel.actionDelegates.enableUndoAction.delegate(on: self) { target, enable in
            target.undoRedoHandler.update(enable: enable)
            target.updateUndoRedo(enable: enable)
        }
    }
}

// MARK: 编辑态
extension AIBackgroundController {
    /* 进入编辑态
     1. 草稿action不序列化
     2. Transaction将所有action包起来
     3. 调整画布比例
     4. 切换屏上手势模式
     5. 聚焦当前seg，包括旋转缩放位置信息，隐藏其他片段
     6. 初始化相关数据
     7. 接管面板undoredo
    */
    private func enterEditMode() {
        guard !viewModel.isEditMode, let view = view, context.player?.isInTransaction() == false else { return }
        viewModel.isEditMode = true
        context.player?.autoSaveJSON = false
        context.player?.pause()
        context.player?.beginTransaction()
        cleanCurSegUnlessConfigs()
        switchGestureMode()
        setCanvas(viewModel.ratio, force: true)
        context.mattingManager.focusOnSegment(selectedVideoSegment, record: true)
        viewModel.update(originWidth: materialWidth, originHeight: materialHeight)
        updateRatioPanel(with: .original)
        context.mappingTimelineVideoSegmentNotify.notify(obj: context.mappingTimelineVideoSegment)
        handleUndoRedo()
    }
    
    private func exitEditMode() {
        guard viewModel.isEditMode else { return }
        recoverCanvasBackground()
        context.mattingManager.resignFocusOfSegment(selectedVideoSegment)
        context.mappingTimelineVideoSegmentNotify.notify(obj: context.mappingTimelineVideoSegment)
        context.player?.commitTransaction()
        context.player?.autoSaveJSON = true
        updateActionState()
        comboExitAction()
        finishUndoRedo()
        cleanUnlessBackground()
        view = nil
        viewModel.isEditMode = false
        DispatchQueue.main.async { [weak self] in
            self?.switchGestureMode()
        }
    }
}

// MARK: - setup
extension AIBackgroundController {
    private func setupViewModel() {
        viewModel.trackPramasDelegate.getKeyingTypeStr.delegate(on: self) { target, _ in
            return target.aiBackgroundKeyingType()
        }
        viewModel.ratio = .original
        viewModel.panelStatus = .input
        viewModel.needResetPreviewResult = false
        viewModel.needSetMosaicBackground = true
        setupPrompt()
        setupApplied()
        setupAlgorithmType()
    }
    
    private func setupApplied() {
        guard let segment = selectedVideoSegment else { return }
        // 两种模式，已应用背景图或者抠像
        viewModel.update(originalPath: isMatting ? nil : curAlgorithmPath)
        viewModel.needResetPreviewResult = !isMatting
        viewModel.update(enable: isMatting)
    }
    
    private func setupPrompt() {
        // 只有处于非抠像状态才保留上次的 prompt
        guard !isMatting,
              let segment = selectedVideoSegment,
              let config = segment.videoAlgorithm.aiBackgroundConfigs.last else {
            viewModel.prompt = ""
            return
        }
        viewModel.prompt = config.prompt
    }
    
    private func setupAlgorithmType() {
        guard let segment = selectedVideoSegment else { return }
        if segment.isVideoAppliedAIMatting() {
            viewModel.algorithmType = .backgroundAfterAIMatting
        } else if segment.isVideoAppliedCustomMatting() {
            viewModel.algorithmType = .backgroundAfterCustomMatting
        } else if segment.isVideoAppliedChroma() {
            viewModel.algorithmType = .backgroundAfterChromaMatting
        }
    }
    
    private func switchGestureMode() {
        guard viewModel.isEditMode else {
            context.switchGestureMode(.normal)
            return
        }
        if viewModel.panelStatus == .input && !viewModel.needResetPreviewResult {
            context.switchGestureMode(.aiBackground)
        } else {
            context.switchGestureMode(.none)
        }
    }
}

// MARK: Report参数
extension AIBackgroundController {
    func aiBackgroundKeyingType() -> String {
        guard let segment = context.selectedVideoSegment as? SegmentVideo  else { return ""}
        var keyingType = ""
        if segment.isVideoAppliedAIMatting() || segment.isVideoAppliedAIBackgroundBasedAIMatting() {
            keyingType = "smart"
        }
        if segment.isVideoAppliedCustomMatting() || segment.isVideoAppliedAIBackgroundBasedCustomMatting() {
            keyingType = keyingType.count == 0 ? "custom" : keyingType + ", custom"
        }
        if segment.isVideoAppliedChroma() || segment.isVideoAppliedAIBackgroundBasedChromaMatting() {
            keyingType = keyingType.count == 0 ? "chroma" : keyingType + ", chroma"
        }
        return keyingType
    }
}

// MARK: Update
extension AIBackgroundController {
    func updateActionState() {
        viewModel.needCleanLastResult = !findAlgorithm(with: viewModel.originalPath) && viewModel.originalPath.isNotNil
        // 存在算法产物，且不是上次应用的算法产物，且不是抠像状态
        viewModel.needSetCurrentResult = findAlgorithm(with: curAlgorithmPath) && viewModel.originalPath != curAlgorithmPath && !isMatting
    }
    
    private func findAlgorithm(with path: String?) -> Bool {
        guard let path = path, let segment = selectedVideoSegment else { return false }
        return segment.videoAlgorithm.aiBackgroundConfigs.contains(where: { $0.path == path })
    }
    
    private func updateSelectStatus() {
        guard let view = view else { return }
        view.updateSelect(with: curAlgorithmPath)
    }
}

// MARK: - view action
extension AIBackgroundController {
    public func getView() -> MenuPrototype? {
        if let view = view {
            return view
        }
        setupViewModel()
        view = AIBackgroundPanelView(viewModel: viewModel)
        setupDelegate()
        return view
    }

    public func isPanelShow() -> Bool {
        return view.isNotNil
    }
    
    func showPanel(segment: LVMediaSegment) {
        context.player?.pause()
        let menuId: MenuIdentifier = context.draft.isMainVideo(of: segment.segmentID) ? .videoMattingAIBackground : .subVideoMattingAIBackground
        context.menuManager?.push(menuId, menuView: getView())
    }
    
    func dismissPanel(completion: @escaping () -> Void) {
        view?.removeFromContainer(animated: false)
        exitEditMode()
        completion()
    }
    
    func updateRatioPanel(with ratio: LVCanvasRatio) {
        viewModel.ratio = ratio
        view?.update(ratio: ratio)
    }
    
    func tapOnCanvas() {
        view?.textViewResignFirstResponder()
    }
}

extension AIBackgroundController {
    func cleanUnlessBackground() {
        context.draftBucket.removeAsync(forKey: "aiBackground")
    }
}

extension AIBackgroundController {
    func trackResize(_ editResult: EditResult?) {
        guard let editResult = editResult else { return }
        // 中间层旧架构原action的actionID跟LRYA的reqID不一致，驼峰式和下划线区别
        if editResult.actionID == LVVE_SCALE_SEGMENT_ACTION {
            viewModel.trackResize(action: .scale)
        }
        if editResult.actionID == LVVE_ROTATE_SEGMENT_ACTION {
            viewModel.trackResize(action: .rotation)
        }
        if editResult.actionID == LVVE_TRANSLATE_SEGMENT_ACTION {
            viewModel.trackResize(action: .position)
        }
    }
}

// MARK: - 只读属性
extension AIBackgroundController {
    
    var ratio: LVCanvasRatio { context.draft.canvasConfig.ratio }
    
    var selectedVideoSegment: SegmentVideo? { context.selectedVideoSegment as? SegmentVideo }
    
    var originRatio: LVCanvasRatio {
        LVCanvasRatio.nearRatio(size: .init(width: materialWidth, height: materialHeight))
    }
    
    var curBackgroundConfig: AiBackground? {
        guard let segment = selectedVideoSegment else { return nil }
        return segment.videoAlgorithm.aiBackgroundConfigs.last
    }
    
    var curAlgorithmID: String? {
        curBackgroundConfig?.algorithmID
    }
    
    var isMatting: Bool {
        guard let segment = selectedVideoSegment else { return false }
        return segment.isVideoAppliedMatting() || segment.isVideoAppliedChroma()
    }
    
    private var curAlgorithmPath: String? {
        curBackgroundConfig?.path
    }

    private var materialWidth: Int {
        guard let segmentVideo = selectedVideoSegment as? SegmentVideo else { return 1024 }
        return Int(segmentVideo.getRenderMaterialSize(rootPath: context.draft.rootPath).width)
    }
    
    private var materialHeight: Int {
        guard let segmentVideo = selectedVideoSegment as? SegmentVideo else { return 1024 }
        return Int(segmentVideo.getRenderMaterialSize(rootPath: context.draft.rootPath).height)
    }
}



